import sys
import math
import random
import itertools
from typing import Iterable
import os
import torch
import util.misc as utils
from datasets.eval_detection import DetectionEvaluator
from datasets import (coco_base_class_ids, coco_novel_class_ids, \
                      voc_base1_class_ids, voc_novel1_class_ids, \
                      voc_base2_class_ids, voc_novel2_class_ids, \
                      voc_base3_class_ids, voc_novel3_class_ids)

@torch.no_grad()
def sample_support_categories(args, targets, support_images, support_class_ids, support_targets):
    """
    This function is used during training. It does the followings:
    1. Samples the support categories (total num: args.total_num_support; maximum positive num: args.max_pos_support)
       (Insufficient positive support categories will be replaced with negative support categories.)
    2. Filters ground truths of the query images.
       We only keep ground truths whose labels are sampled as support categories.
    3. Samples and pre-processes support_images, support_class_ids, and support_targets.
    """
    # 1. 展平支持集列表
    support_images = list(itertools.chain(*support_images))
    
    # 2. 修改支持集类别ID的处理 - 不再假设每个图像只有一个类别
    # 原始错误代码: support_class_ids = torch.cat(support_class_ids, dim=0).tolist()
    
    # 新的处理方式：每个支持图像可能有多个类别
    support_class_ids_list = []  # 存储每个支持图像的类别ID列表
    for class_ids_tensor in support_class_ids:
        # 确保所有类别ID在有效范围内
        valid_class_ids = [cid.item() for cid in class_ids_tensor.unique() if cid < args.num_classes]
        support_class_ids_list.append(valid_class_ids)
    
    support_targets = list(itertools.chain(*support_targets))
    # 3. 获取查询图像中的所有正类标签
    positive_labels = torch.cat([target['labels'] for target in targets], dim=0).unique()
    num_positive_labels = positive_labels.shape[0]
    positive_labels_list = positive_labels.tolist()
    
    # 4. 获取所有可能的负类标签（出现在支持集中但不在查询图像中）
    all_support_labels = set(itertools.chain.from_iterable(support_class_ids_list))
    negative_labels_list = list(all_support_labels - set(positive_labels_list))
    num_negative_labels = len(negative_labels_list)
    # 5. 创建索引映射（支持图像索引 -> 类别ID）
    image_index_to_class = []
    for img_idx, class_ids in enumerate(support_class_ids_list):
        for class_id in class_ids:
            image_index_to_class.append((img_idx, class_id))
    # 6. 重新组织元支持集
    meta_support_images, meta_support_class_ids, meta_support_targets = list(), list(), list()
    
    for _ in range(args.episode_num):
        NUM_POS = random.randint(max(0, args.episode_size - num_negative_labels),
                                 min(num_positive_labels, args.episode_size))
        NUM_NEG = args.episode_size - NUM_POS
        # 7. 采样正类支持图像-类别对（确保类别不重复）
        selected_pairs = set()
        selected_classes = set()
        
        # 采样正类
        pos_candidates = [(idx, cid) for idx, cid in image_index_to_class if cid in positive_labels_list]
        while len(selected_pairs) < NUM_POS and pos_candidates:
            candidate = random.choice(pos_candidates)
            if candidate[1] not in selected_classes:  # 确保类别不重复
                selected_pairs.add(candidate)
                selected_classes.add(candidate[1])
            pos_candidates.remove(candidate)
        
        # 采样负类
        neg_candidates = [(idx, cid) for idx, cid in image_index_to_class if cid in negative_labels_list]
        while len(selected_pairs) < NUM_POS + NUM_NEG and neg_candidates:
            candidate = random.choice(neg_candidates)
            if candidate[1] not in selected_classes:  # 确保类别不重复
                selected_pairs.add(candidate)
                selected_classes.add(candidate[1])
            neg_candidates.remove(candidate)
        
        # 8. 如果采样不足，随机填充
        while len(selected_pairs) < args.episode_size:
            candidate = random.choice(image_index_to_class)
            if candidate not in selected_pairs:
                selected_pairs.add(candidate)
        
        # 9. 准备选中的支持图像数据
        selected_indices = set(idx for idx, _ in selected_pairs)
        selected_support_images = [support_images[idx] for idx in selected_indices]
        selected_support_class_ids = [cid for _, cid in selected_pairs]
        
        # 10. 创建支持目标（只包含选中的类别）
        selected_support_targets = []
        for idx in selected_indices:
            target = support_targets[idx]
            # 过滤出选中的类别
            mask = torch.tensor([label.item() in selected_classes for label in target['labels']], dtype=torch.bool)
            filtered_target = {
                'boxes': target['boxes'][mask] if 'boxes' in target else None,
                'labels': target['labels'][mask] if 'labels' in target else None,
                'size': target['size'] if 'size' in target else torch.tensor([0, 0]),
                'orig_size': target['orig_size'] if 'orig_size' in target else torch.tensor([0, 0])
            }
            selected_support_targets.append(filtered_target)
        
        meta_support_images += selected_support_images
        meta_support_class_ids += selected_support_class_ids
        meta_support_targets += selected_support_targets
    # 11. 转换为张量
    meta_support_images = utils.nested_tensor_from_tensor_list(meta_support_images)
    meta_support_class_ids = torch.tensor(meta_support_class_ids)
    return targets, meta_support_images, meta_support_class_ids, meta_support_targets
    # support_images = list(itertools.chain(*support_images))
    # support_class_ids = torch.cat(support_class_ids, dim=0).tolist()
    # support_targets = list(itertools.chain(*support_targets))

    # positive_labels = torch.cat([target['labels'] for target in targets], dim=0).unique()
    # num_positive_labels = positive_labels.shape[0]
    # positive_labels_list = positive_labels.tolist()
    # negative_labels_list = list(set(support_class_ids) - set(positive_labels_list))
    # num_negative_labels = len(negative_labels_list)

    # positive_label_indexes = [i for i in list(range(len(support_images))) if support_class_ids[i] in positive_labels_list]
    # negative_label_indexes = [i for i in list(range(len(support_images))) if support_class_ids[i] in negative_labels_list]

    # meta_support_images, meta_support_class_ids, meta_support_targets = list(), list(), list()
    # for _ in range(args.episode_num):
    #     NUM_POS = random.randint(max(0, args.episode_size - num_negative_labels),
    #                              min(num_positive_labels, args.episode_size))
    #     NUM_NEG = args.episode_size - NUM_POS

    #     # Sample positive support classes: make sure in every episode, there is no repeated category
    #     while True:
    #         pos_support_indexes = random.sample(positive_label_indexes, NUM_POS)
    #         if NUM_POS == len(set([support_class_ids[i] for i in pos_support_indexes])):
    #             break

    #     # Sample negative support classes: try our best to ensure in every episode there is no repeated category
    #     num_trial = 0
    #     while num_trial < 50:
    #         neg_support_indexes = random.sample(negative_label_indexes, NUM_NEG)
    #         if NUM_NEG == len(set([support_class_ids[i] for i in neg_support_indexes])):
    #             break
    #         else:
    #             num_trial += 1

    #     support_indexes = pos_support_indexes + neg_support_indexes
    #     random.shuffle(support_indexes)

    #     selected_support_images = [support_images[i] for i in support_indexes]
    #     selected_support_class_ids = [support_class_ids[i] for i in support_indexes]
    #     selected_support_targets = [support_targets[i] for i in support_indexes]

    #     meta_support_images += selected_support_images
    #     meta_support_class_ids += selected_support_class_ids
    #     meta_support_targets += selected_support_targets

    # meta_support_images = utils.nested_tensor_from_tensor_list(meta_support_images)
    # meta_support_class_ids = torch.tensor(meta_support_class_ids)

    # return targets, meta_support_images, meta_support_class_ids, meta_support_targets


def train_one_epoch(args,
                    model: torch.nn.Module,
                    criterion: torch.nn.Module,
                    dataloader: Iterable,
                    optimizer: torch.optim.Optimizer,
                    device: torch.device,
                    epoch: int,
                    max_norm: float = 0):
    model.train()
    criterion.train()
    metric_logger = utils.MetricLogger(delimiter="  ")
    metric_logger.add_meter('lr', utils.SmoothedValue(window_size=1, fmt='{value:.6f}'))
    metric_logger.add_meter('class_error', utils.SmoothedValue(window_size=1, fmt='{value:.2f}'))
    metric_logger.add_meter('grad_norm', utils.SmoothedValue(window_size=1, fmt='{value:.2f}'))
    header = 'Epoch: [{}]'.format(epoch)
    print_freq = 50

    # 添加数据集完整性检查
    print("\n===== 开始数据集完整性检查 =====")
    dataset = dataloader.dataset
    all_class_ids = set(dataset.coco.getCatIds())
    
    # 1. 检查每个类别的样本数量
    class_distribution = {}
    empty_classes = []
    
    for class_id in all_class_ids:
        num_samples = len(dataset.anns_by_class.get(class_id, []))
        class_distribution[class_id] = num_samples
        if num_samples == 0:
            empty_classes.append(class_id)
    
    # 打印类别分布
    print("\n类别分布统计:")
    for class_id, count in sorted(class_distribution.items()):
        class_name = dataset.coco.loadCats(class_id)[0]['name']
        print(f"类别 {class_id} ({class_name}): {count} 个样本")
    
    if empty_classes:
        print(f"\n警告: 发现 {len(empty_classes)} 个空类别:")
        for class_id in empty_classes:
            class_name = dataset.coco.loadCats(class_id)[0]['name']
            print(f" - 类别 {class_id} ({class_name})")
    else:
        print("\n所有类别至少有一个样本")
    
    # 2. 检查图像文件是否存在
    missing_images = []
    for i, img_info in enumerate(dataset.coco.dataset['images']):
        img_path = os.path.join('/home/<USER>/Meta-DETR/data/rubbish/images', img_info['file_name'])
        if not os.path.exists(img_path):
            missing_images.append(img_path)
        # 每1000张图片打印一次进度
        if i > 0 and i % 1000 == 0:
            print(f"已检查 {i}/{len(dataset.coco.dataset['images'])} 张图片...")
    
    if missing_images:
        print(f"\n警告: 发现 {len(missing_images)} 张图片缺失:")
        for path in missing_images[:5]:  # 只显示前5个缺失文件
            print(f" - {path}")
        print(f"...(只显示前5个，共{len(missing_images)}个)")
    else:
        print("\n所有图片文件都存在")
    
    print("===== 数据集完整性检查完成 =====\n")

    for samples, targets, support_images, support_class_ids, support_targets in metric_logger.log_every(dataloader, print_freq, header):

        # * Sample Support Categories;
        # * Filters Targets (only keep GTs within support categories);
        # * Samples Support Images and Targets
        targets, support_images, support_class_ids, support_targets = \
            sample_support_categories(args, targets, support_images, support_class_ids, support_targets)

        samples = samples.to(device)
        targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
        support_images = support_images.to(device)
        support_class_ids = support_class_ids.to(device)
        support_targets = [{k: v.to(device) for k, v in t.items()} for t in support_targets]

        outputs = model(samples, targets=targets, supp_samples=support_images, supp_class_ids=support_class_ids, supp_targets=support_targets)
        loss_dict = criterion(outputs)
        weight_dict = criterion.weight_dict
        losses = sum(loss_dict[k] * weight_dict[k] for k in loss_dict.keys() if k in weight_dict)

        # reduce losses over all GPUs for logging purposes
        loss_dict_reduced = utils.reduce_dict(loss_dict)
        loss_dict_reduced_unscaled = {f'{k}_unscaled': v for k, v in loss_dict_reduced.items()}
        loss_dict_reduced_scaled = {k: v * weight_dict[k] for k, v in loss_dict_reduced.items() if k in weight_dict}
        losses_reduced_scaled = sum(loss_dict_reduced_scaled.values())

        loss_value = losses_reduced_scaled.item()

        if not math.isfinite(loss_value):
            print("Loss is NaN - {}. \nTraining terminated unexpectedly.\n".format(loss_value))
            print("loss dict:")
            print(loss_dict_reduced)
            sys.exit(1)

        optimizer.zero_grad()
        losses.backward()
        if max_norm > 0:
            grad_total_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
        else:
            grad_total_norm = utils.get_total_grad_norm(model.parameters(), max_norm)
        optimizer.step()

        metric_logger.update(loss=loss_value, **loss_dict_reduced_scaled, **loss_dict_reduced_unscaled)
        metric_logger.update(class_error=loss_dict_reduced['class_error'])
        metric_logger.update(lr=optimizer.param_groups[0]["lr"])
        metric_logger.update(grad_norm=grad_total_norm)

    # gather the stats from all processes
    metric_logger.synchronize_between_processes()
    print("Averaged stats:", metric_logger)

    del support_images
    del support_class_ids
    del support_targets
    del samples
    del targets
    del outputs
    del weight_dict
    del grad_total_norm
    del loss_value
    del losses
    del loss_dict
    del loss_dict_reduced
    del loss_dict_reduced_scaled
    del loss_dict_reduced_unscaled

    return {k: meter.global_avg for k, meter in metric_logger.meters.items()}


@torch.no_grad()
def evaluate(args, model, criterion, postprocessors, dataloader, support_data_loader, base_ds, device, type='all'):
    model.eval()
    criterion.eval()

    # First: Obtain Category Codes for All Categories to Detect
    support_iter = iter(support_data_loader)
    all_category_codes_final = []
    print("Extracting support category codes...")
    number_of_supports = 100  # This is the number of support images to use for each category. Need be large enough.
    for i in range(number_of_supports):
        try:
            support_images, support_class_ids, support_targets = next(support_iter)
        except:
            support_iter = iter(support_data_loader)
            support_images, support_class_ids, support_targets = next(support_iter)
        support_images = [support_image.squeeze(0) for support_image in support_images]
        support_class_ids = support_class_ids.squeeze(0).to(device)
        support_targets = [{k: v.squeeze(0) for k, v in t.items()} for t in support_targets]
        num_classes = support_class_ids.shape[0]
        num_episode = math.ceil(num_classes / args.episode_size)
        category_codes_final = []
        support_class_ids_final = []
        for i in range(num_episode):
            if (args.episode_size * (i + 1)) <= num_classes:
                support_images_ = utils.nested_tensor_from_tensor_list(
                    support_images[(args.episode_size * i): (args.episode_size * (i + 1))]
                ).to(device)
                support_targets_ = [
                    {k: v.to(device) for k, v in t.items()} for t in support_targets[(args.episode_size * i): (args.episode_size * (i + 1))]
                ]
                support_class_ids_ = support_class_ids[(args.episode_size * i): (args.episode_size* (i + 1))]
            else:
                support_images_ = utils.nested_tensor_from_tensor_list(
                    support_images[-args.episode_size:]
                ).to(device)
                support_targets_ = [
                    {k: v.to(device) for k, v in t.items()} for t in support_targets[-args.episode_size:]
                ]
                support_class_ids_ = support_class_ids[-args.episode_size:]
            if isinstance(model, torch.nn.parallel.DistributedDataParallel):
                category_code = model.module.compute_category_codes(support_images_, support_targets_)
            else:
                category_code = model.compute_category_codes(support_images_, support_targets_)
            category_code = torch.stack(category_code, dim=0)   # (num_enc_layer, args.total_num_support, d)
            category_codes_final.append(category_code)
            support_class_ids_final.append(support_class_ids_)
        support_class_ids_final = torch.cat(support_class_ids_final, dim=0)
        category_codes_final = torch.cat(category_codes_final, dim=1)  # (num_enc_layer, num_episode x args.total_num_support, d)
        all_category_codes_final.append(category_codes_final)

    if args.num_feature_levels == 1:
        all_category_codes_final = torch.stack(all_category_codes_final, dim=0)  # (number_of_supports, num_enc_layer, num_episode x args.total_num_support, d)
        all_category_codes_final = torch.mean(all_category_codes_final, 0, keepdims=False)
        all_category_codes_final = list(torch.unbind(all_category_codes_final, dim=0))
    elif args.num_feature_levels == 4:
        raise NotImplementedError
    else:
        raise NotImplementedError
    print("Completed extracting category codes. Start Inference...")

    metric_logger = utils.MetricLogger(delimiter="  ")
    metric_logger.add_meter('class_error', utils.SmoothedValue(window_size=1, fmt='{value:.2f}'))
    header = 'Test:'

    iou_types = tuple(k for k in ('bbox',) if k in postprocessors.keys())
    evaluator = DetectionEvaluator(base_ds, iou_types)
    if type == 'all':
        pass  # To evaluate all categories, no need to change params of the evaluator
    elif type == 'base':
        if args.dataset_file == 'coco_base':
            evaluator.coco_eval['bbox'].params.catIds = coco_base_class_ids
        elif args.dataset_file == 'voc_base1':
            evaluator.coco_eval['bbox'].params.catIds = voc_base1_class_ids
        elif args.dataset_file == 'voc_base2':
            evaluator.coco_eval['bbox'].params.catIds = voc_base2_class_ids
        elif args.dataset_file == 'voc_base3':
            evaluator.coco_eval['bbox'].params.catIds = voc_base3_class_ids
        else:
            raise ValueError
    elif type == 'novel':
        if args.dataset_file == 'coco_base' or args.dataset_file == 'coco':
            evaluator.coco_eval['bbox'].params.catIds = coco_novel_class_ids
        elif args.dataset_file == 'voc_base1':
            evaluator.coco_eval['bbox'].params.catIds = voc_novel1_class_ids
        elif args.dataset_file == 'voc_base2':
            evaluator.coco_eval['bbox'].params.catIds = voc_novel2_class_ids
        elif args.dataset_file == 'voc_base3':
            evaluator.coco_eval['bbox'].params.catIds = voc_novel3_class_ids
        else:
            raise ValueError
    else:
        raise ValueError("Type must be 'all', 'base' or 'novel'!")

    print_freq = 50

    for samples, targets in metric_logger.log_every(dataloader, print_freq, header):

        samples = samples.to(device)
        targets = [{k: v.to(device) for k, v in t.items()} for t in targets]

        outputs = model(samples, targets=targets, supp_class_ids=support_class_ids_final, category_codes=all_category_codes_final)
        loss_dict = criterion(outputs)
        weight_dict = criterion.weight_dict

        # reduce losses over all GPUs for logging purposes
        loss_dict_reduced = utils.reduce_dict(loss_dict)
        loss_dict_reduced_scaled = {k: v * weight_dict[k] for k, v in loss_dict_reduced.items() if k in weight_dict}
        loss_dict_reduced_unscaled = {f'{k}_unscaled': v for k, v in loss_dict_reduced.items()}
        metric_logger.update(loss=sum(loss_dict_reduced_scaled.values()),
                             **loss_dict_reduced_scaled,
                             **loss_dict_reduced_unscaled)
        metric_logger.update(class_error=loss_dict_reduced['class_error'])

        orig_target_sizes = torch.stack([t["orig_size"] for t in targets], dim=0)
        results = postprocessors['bbox'](outputs, orig_target_sizes)
        res = {target['image_id'].item(): output for target, output in zip(targets, results)}
        if evaluator is not None:
            evaluator.update(res)

    # gather the stats from all processes
    metric_logger.synchronize_between_processes()
    print("Averaged stats:", metric_logger)
    if evaluator is not None:
        evaluator.synchronize_between_processes()

    # accumulate predictions from all images
    if evaluator is not None:
        if type == 'all':
            print("\n\n\n\n * ALL Categories:")
        elif type == 'base':
            print("\n\n\n\n * Base Categories:")
        elif type == 'novel':
            print("\n\n\n\n * Novel Categories:")
        else:
            raise ValueError("Type must be 'all', 'base' or 'novel'!")
        evaluator.accumulate()
        evaluator.summarize()
    stats = {k: meter.global_avg for k, meter in metric_logger.meters.items()}
    if evaluator is not None:
        if 'bbox' in postprocessors.keys():
            stats['coco_eval_bbox'] = evaluator.coco_eval['bbox'].stats.tolist()

    del support_images
    del support_class_ids
    del support_targets
    del samples
    del targets
    del outputs
    del weight_dict
    del loss_dict
    del loss_dict_reduced
    del loss_dict_reduced_scaled
    del loss_dict_reduced_unscaled
    del category_code
    del category_codes_final
    del all_category_codes_final
    del orig_target_sizes
    del res
    del results
    torch.cuda.empty_cache()

    return stats, evaluator

